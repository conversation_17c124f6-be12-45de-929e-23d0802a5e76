# Task Completion Checklist

## <PERSON><PERSON> hoàn thành một task, c<PERSON><PERSON> thực hiện:

### 1. Code Quality
- [ ] Kiểm tra code style theo PEP 8
- [ ] <PERSON><PERSON><PERSON><PERSON> comments và docstrings
- [ ] Remove unused imports và variables
- [ ] Kiểm tra error handling

### 2. Testing
- [ ] Test script với dữ liệu thật
- [ ] Kiểm tra database connection
- [ ] Verify data insertion
- [ ] Test error scenarios

### 3. Documentation
- [ ] Update README nếu cần
- [ ] Document new functions/classes
- [ ] Update workflow file trong memory_bank
- [ ] Add usage examples

### 4. Database
- [ ] Kiểm tra table structure
- [ ] Verify foreign key constraints
- [ ] Test data integrity
- [ ] Backup data nếu cần

### 5. Environment
- [ ] Update requirements.txt
- [ ] Kiểm tra .env configuration
- [ ] Test trên clean environment
- [ ] Document setup steps

### 6. Logging
- [ ] Kiểm tra log output
- [ ] Verify log levels
- [ ] Test log rotation nếu có
- [ ] Document log locations