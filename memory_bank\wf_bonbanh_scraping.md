# Workflow: Bonbanh.com Scraping System

## Tổng quan

Hệ thống scraping dữ liệu hãng xe (brands), dòng xe (models) và phiên bản xe (versions) từ website bonbanh.com, lưu trữ vào MySQL database với 3 bảng có quan hệ foreign key.

## Cấu trúc dự án

```
xehoi_pro_create_post_4/
├── .env                    # Cấu hình môi trường
├── requirements.txt        # Dependencies Python
├── config.py              # Module cấu hình
├── utils.py               # Các hàm tiện ích
├── check_data.py          # Script kiểm tra dữ liệu brands/models
├── check_versions_data.py  # Script kiểm tra dữ liệu versions
├── demo.py                # Script demo showcase
├── README.md              # Hướng dẫn sử dụng
├── scrapers/              # Thư mục chứa các scraper
│   ├── __init__.py
│   ├── bonbanh_brands_scraper.py    # Scraper cho brands và models
│   └── bonbanh_versions_scraper.py  # Scraper cho versions và years
├── database/              # Thư mục chứa database scripts
│   ├── __init__.py
│   ├── database_setup.py  # Script thiết lập database
│   └── models.py          # Định nghĩa schema
├── logs/                  # Thư mục chứa log files
└── memory_bank/           # Thư mục chứa workflow docs
    └── wf_bonbanh_scraping.md
```

## Database Schema

### Bảng `brands`

```sql
CREATE TABLE brands (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_slug (slug)
);
```

### Bảng `models`

```sql
CREATE TABLE models (
    id INT AUTO_INCREMENT PRIMARY KEY,
    brand_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE CASCADE,
    UNIQUE KEY unique_brand_model (brand_id, name),
    INDEX idx_brand_id (brand_id),
    INDEX idx_name (name),
    INDEX idx_slug (slug),
    INDEX idx_url (url)
);
```

### Bảng `car_version_year`

```sql
CREATE TABLE car_version_year (
    id INT AUTO_INCREMENT PRIMARY KEY,
    brand_id INT NOT NULL,
    model_id INT NOT NULL,
    version_name VARCHAR(200) NOT NULL,
    year INT NOT NULL,
    url VARCHAR(500) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE CASCADE,
    FOREIGN KEY (model_id) REFERENCES models(id) ON DELETE CASCADE,
    UNIQUE KEY unique_version (model_id, version_name, year),
    INDEX idx_brand_model (brand_id, model_id),
    INDEX idx_year (year),
    INDEX idx_version_name (version_name)
);
```

## Cách sử dụng

### 1. Cài đặt dependencies

```bash
pip install -r requirements.txt
```

### 2. Cấu hình môi trường

Đảm bảo file `.env` có đầy đủ thông tin:

```
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=xehoi_pro
DB_USERNAME=root
DB_PASSWORD=pwdpwd
SCRAPING_DELAY=2
MAX_RETRIES=3
TIMEOUT=30
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
LOG_LEVEL=INFO
LOG_FILE=logs/scraper.log
```

### 3. Thiết lập database

```bash
python database/database_setup.py
```

- Chọn option 1: Giữ nguyên dữ liệu cũ (nâng cấp schema)
- Chọn option 2: Xóa và tạo lại toàn bộ

### 4. Chạy scraping brands và models

```bash
python scrapers/bonbanh_brands_scraper.py
```

### 5. Chạy scraping versions (sau khi có brands và models)

```bash
# Scrape tất cả models
python scrapers/bonbanh_versions_scraper.py

# Hoặc giới hạn số lượng models để test
python scrapers/bonbanh_versions_scraper.py --limit 10

# Hoặc scrape từ vị trí cụ thể (resume)
python scrapers/bonbanh_versions_scraper.py --offset 50 --limit 20
```

### 6. Kiểm tra dữ liệu

```bash
# Kiểm tra brands và models
python check_data.py

# Kiểm tra versions
python check_versions_data.py

# Demo showcase
python demo.py
```

## Quy trình scraping

### Bước 1: Scrape brands từ trang chủ

- Truy cập https://bonbanh.com/
- Tìm tất cả links có dạng `oto/brand-name`
- Lọc và validate tên brands
- Lưu vào bảng `brands`

### Bước 2: Scrape models cho từng brand

- Truy cập trang của từng brand: `https://bonbanh.com/oto/brand-name`
- Tìm danh sách models trong sidebar
- Lọc links có dạng `oto/brand-model`
- Validate và lưu vào bảng `models` với URL

### Bước 3: Scrape versions cho từng model

- Truy cập trang của từng model từ URL đã lưu
- Tìm phần "{Model} theo năm" ở cuối trang
- Scrape danh sách years (links dạng `oto/brand-model-nam-YYYY`)
- Với mỗi year, truy cập để tìm versions trong tiêu đề tin đăng
- Trích xuất tên version từ tiêu đề (VD: "2.0 TFSI", "1.8L", "Quattro")
- Validate và lưu vào bảng `car_version_year`

## Các tính năng chính

### Error Handling

- Retry logic với exponential backoff
- Logging chi tiết các lỗi
- Graceful handling khi không thể truy cập trang

### Data Validation

- Validate tên brands và models
- Kiểm tra duplicate trước khi insert
- Sanitize dữ liệu đầu vào

### Rate Limiting

- Delay configurable giữa các requests
- Tránh bị block bởi website

### Logging

- Log file trong thư mục `logs/`
- Log level configurable
- Thống kê kết quả scraping

## Cấu trúc HTML được scraping

### Trang chủ (brands)

```html
<a href="oto/honda">Honda</a>
<a href="oto/toyota">Toyota</a>
<a href="oto/bmw">BMW</a>
```

### Trang brand (models)

```html
<a href="oto/honda-city">City</a>
<a href="oto/honda-civic">Civic</a>
<a href="oto/honda-crv">CRV</a>
```

### Trang model (years)

```html
<a href="oto/honda-city-nam-2024">2024</a>
<a href="oto/honda-city-nam-2023">2023</a>
<a href="oto/honda-city-nam-2022">2022</a>
```

### Trang year (versions)

```html
<h3>Honda City 1.5 CVT - 2024</h3>
<h3>Honda City 1.5 RS - 2024</h3>
<h3>Honda City 1.5 L - 2024</h3>
```

## Kết quả thực tế đã đạt được

### Thống kê tổng quan - Brands & Models

- **27 brands** đã được scrape thành công
- **450 models** đã được scrape và lưu vào database
- Thời gian scraping brands: ~3 phút
- Tỷ lệ thành công: 100%

### Thống kê tổng quan - Versions (Test với 5 models)

- **5 models** đã được xử lý (Audi A4, A5, A6, A7, A8)
- **85 versions** đã được scrape thành công
- **0 models** bị bỏ qua
- Thời gian scraping versions: ~2 phút
- Tỷ lệ thành công: 100%

### Top 10 brands có nhiều models nhất

1. **Toyota** - 30 models (Vios, Fortuner, Innova, Camry, Corolla Cross, Land Cruiser, v.v.)
2. **Ford** - 24 models (Ranger, Everest, Territory, Transit, EcoSport, Focus, v.v.)
3. **Kia** - 24 models (Morning, Seltos, Sorento, Cerato, Sportage, v.v.)
4. **Nissan** - 24 models (Navara, Sunny, Almera, X-trail, Terra, Teana, v.v.)
5. **Hyundai** - 24 models (SantaFe, Accent, i10, Tucson, Elantra, Creta, v.v.)
6. **Mitsubishi** - 24 models (Xpander, Triton, Outlander, Attrage, Pajero Sport, v.v.)
7. **Mercedes Benz** - 20 models (C class, E class, G class, GLB, GLC, GLE, v.v.)
8. **Chevrolet** - 20 models (Spark, Cruze, Captiva, Colorado, Aveo, v.v.)
9. **VinFast** - 20 models (Fadil, Lux A 2.0, Lux SA 2.0, VF3, VF5, VF6, VF7, VF8, VF9, v.v.)
10. **Mazda** - 20 models (CX5, CX8, Mazda3, Mazda6, CX30, v.v.)

### Danh sách đầy đủ 27 brands

Audi, Bentley, BMW, Chevrolet, Daewoo, Ford, Honda, Hyundai, Isuzu, Jeep, Kia, LandRover, Lexus, Mazda, Mercedes Benz, MG, Mini, Mitsubishi, Nissan, Peugeot, Porsche, Subaru, Suzuki, Toyota, VinFast, Volkswagen, Volvo

## Troubleshooting

### Lỗi kết nối database

- Kiểm tra MySQL service đang chạy
- Verify thông tin trong file .env
- Đảm bảo database `xehoi_pro` đã được tạo

### Lỗi scraping

- Kiểm tra kết nối internet
- Website có thể thay đổi cấu trúc HTML
- Tăng SCRAPING_DELAY nếu bị rate limit

### Lỗi encoding

- Đảm bảo database sử dụng utf8mb4
- Python file encoding là UTF-8

## Maintenance

### Cập nhật dữ liệu

- Chạy lại scraper định kỳ để cập nhật
- Sử dụng option "Giữ nguyên dữ liệu cũ" để không mất dữ liệu

### Monitoring

- Kiểm tra log files thường xuyên
- Monitor database size và performance

### Backup

- Backup database trước khi chạy scraping lớn
- Export dữ liệu quan trọng

## Tối ưu hóa

### Performance

- Sử dụng connection pooling cho database
- Implement caching cho các trang đã scrape
- Parallel processing cho multiple brands

### Reliability

- Implement checkpointing để resume scraping
- Add health checks cho website
- Notification system khi scraping fail

## Security

- Rotate User-Agent strings
- Implement proxy rotation nếu cần
- Respect robots.txt và rate limits
