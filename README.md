# Bonbanh.com Scraper - <PERSON><PERSON> thống Scraping Dữ liệu Xe Hơi

Hệ thống scraping dữ liệu hãng xe (brands) và dòng xe (models) từ website bonbanh.com, lưu trữ vào MySQL database.

## 🎯 Mục đích

- Scraping dữ liệu brands và models từ bonbanh.com
- Lưu trữ có cấu trúc vào MySQL database
- Phục vụ cho việc tạo post về xe hơi

## 📊 Kết quả đã đạt được

- ✅ **27 brands** đã được scrape thành công
- ✅ **450 models** đã được scrape và lưu vào database
- ✅ Thời gian scraping: ~3 phút
- ✅ Tỷ lệ thành công: 100%

## 🏗️ Cấu trúc Database

### Bảng `brands`
```sql
CREATE TABLE brands (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Bảng `models`
```sql
CREATE TABLE models (
    id INT AUTO_INCREMENT PRIMARY KEY,
    brand_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE CASCADE
);
```

## 🚀 Cách sử dụng

### 1. Cài đặt dependencies
```bash
pip install -r requirements.txt
```

### 2. Cấu hình môi trường
Đảm bảo file `.env` có đầy đủ thông tin:
```env
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=xehoi_pro
DB_USERNAME=root
DB_PASSWORD=pwdpwd
SCRAPING_DELAY=2
MAX_RETRIES=3
TIMEOUT=30
```

### 3. Thiết lập database
```bash
python database_setup.py
```

### 4. Chạy scraping
```bash
python scraper.py
```

### 5. Kiểm tra dữ liệu
```bash
python check_data.py
```

## 📁 Cấu trúc dự án

```
xehoi_pro_create_post_4/
├── .env                    # Cấu hình môi trường
├── requirements.txt        # Dependencies Python
├── config.py              # Module cấu hình
├── utils.py               # Các hàm tiện ích
├── database_setup.py      # Script thiết lập database
├── scraper.py             # Script chính scraping
├── check_data.py          # Script kiểm tra dữ liệu
├── logs/                  # Thư mục chứa log files
└── memory_bank/           # Thư mục chứa workflow docs
    └── wf_bonbanh_scraping.md
```

## 🏆 Top 10 Brands có nhiều models nhất

1. **Toyota** - 30 models
2. **Ford** - 24 models  
3. **Kia** - 24 models
4. **Nissan** - 24 models
5. **Hyundai** - 24 models
6. **Mitsubishi** - 24 models
7. **Mercedes Benz** - 20 models
8. **Chevrolet** - 20 models
9. **VinFast** - 20 models
10. **Mazda** - 20 models

## 🔧 Tính năng chính

- **Error Handling**: Retry logic với exponential backoff
- **Data Validation**: Validate tên brands và models
- **Rate Limiting**: Delay configurable giữa các requests
- **Logging**: Log chi tiết các hoạt động
- **Duplicate Handling**: Kiểm tra duplicate trước khi insert

## 📝 Logging

- Log file: `logs/scraper.log`
- Log level: INFO (configurable)
- Thống kê kết quả scraping
- Chi tiết lỗi và warning

## 🛠️ Troubleshooting

### Lỗi kết nối database
- Kiểm tra MySQL service đang chạy
- Verify thông tin trong file .env
- Đảm bảo database `xehoi_pro` đã được tạo

### Lỗi scraping
- Kiểm tra kết nối internet
- Website có thể thay đổi cấu trúc HTML
- Tăng SCRAPING_DELAY nếu bị rate limit

## 📈 Tối ưu hóa

### Performance
- Sử dụng connection pooling cho database
- Implement caching cho các trang đã scrape
- Parallel processing cho multiple brands

### Reliability
- Implement checkpointing để resume scraping
- Add health checks cho website
- Notification system khi scraping fail

## 🔒 Security
- Rotate User-Agent strings
- Implement proxy rotation nếu cần
- Respect robots.txt và rate limits

## 📞 Liên hệ

Nếu có vấn đề hoặc cần hỗ trợ, vui lòng tạo issue hoặc liên hệ qua email.

---

**Phát triển bởi**: Serena AI Assistant  
**Ngày tạo**: 06/08/2025  
**Phiên bản**: 1.0.0
