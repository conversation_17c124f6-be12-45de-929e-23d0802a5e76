"""
Bonbanh.com scraper for car versions and years
Scraping dữ liệu phiên bản xe và năm từ bonbanh.com
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pymysql
import logging
import re
from bs4 import BeautifulSoup
from config import Config
from utils import ScrapingUtils
import time

# Setup logging
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(Config.LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BonbanhVersionsScraper:
    """Scraper class for car versions and years from bonbanh.com"""
    
    def __init__(self):
        self.config = Config.get_db_config()
        self.connection = None
        self.session = ScrapingUtils.create_session()
        self.versions_scraped = 0
        self.models_processed = 0
        self.models_skipped = 0
    
    def connect_db(self):
        """Kết nối đến database"""
        try:
            self.connection = pymysql.connect(**self.config)
            logger.info("Kết nối database thành công")
            return True
        except Exception as e:
            logger.error(f"Lỗi kết nối database: {e}")
            return False
    
    def disconnect_db(self):
        """Đóng kết nối database"""
        if self.connection:
            self.connection.close()
            logger.info("Đã đóng kết nối database")
    
    def get_models_to_process(self, limit=None, offset=0):
        """
        Lấy danh sách models cần xử lý
        
        Args:
            limit (int): Số lượng models tối đa
            offset (int): Vị trí bắt đầu
            
        Returns:
            list: Danh sách models
        """
        try:
            with self.connection.cursor() as cursor:
                # Lấy models chưa được scrape versions (chưa có trong car_version_year)
                sql = """
                    SELECT DISTINCT m.id, m.name, m.url, b.id as brand_id, b.name as brand_name
                    FROM models m
                    JOIN brands b ON m.brand_id = b.id
                    LEFT JOIN car_version_year cv ON m.id = cv.model_id
                    WHERE m.url IS NOT NULL 
                    AND cv.id IS NULL
                    ORDER BY b.name, m.name
                """
                
                if limit:
                    sql += f" LIMIT {limit}"
                if offset:
                    sql += f" OFFSET {offset}"
                
                cursor.execute(sql)
                models = cursor.fetchall()
                
                logger.info(f"Tìm thấy {len(models)} models cần xử lý versions")
                return models
                
        except Exception as e:
            logger.error(f"Lỗi lấy danh sách models: {e}")
            return []
    
    def extract_years_from_model_page(self, model_url):
        """
        Trích xuất danh sách years từ trang model
        
        Args:
            model_url (str): URL của model
            
        Returns:
            list: Danh sách dict chứa thông tin years
        """
        logger.info(f"Scraping years từ: {model_url}")
        
        # Delay để tránh bị block
        ScrapingUtils.delay_between_requests()
        
        soup = ScrapingUtils.get_page_content(model_url, self.session)
        if not soup:
            logger.error(f"Không thể lấy trang model: {model_url}")
            return []
        
        years = []
        
        # Tìm phần "theo năm" ở cuối trang
        # Dựa trên HTML đã phân tích, tìm section chứa links năm
        year_sections = soup.find_all('a', href=True)
        
        for link in year_sections:
            href = link.get('href', '')
            text = ScrapingUtils.clean_text(link.get_text())
            
            # Tìm links có pattern: oto/brand-model-nam-YYYY
            if '-nam-' in href and text.isdigit():
                year = int(text)
                
                # Validate năm (từ 1990 đến 2030)
                if 1990 <= year <= 2030:
                    year_url = f"{Config.BASE_URL}/{href}" if not href.startswith('http') else href
                    
                    year_info = {
                        'year': year,
                        'url': year_url
                    }
                    
                    years.append(year_info)
                    logger.info(f"Tìm thấy year: {year}")
        
        # Loại bỏ duplicate và sắp xếp
        unique_years = {}
        for year_info in years:
            unique_years[year_info['year']] = year_info
        
        sorted_years = sorted(unique_years.values(), key=lambda x: x['year'], reverse=True)
        
        logger.info(f"Đã scrape {len(sorted_years)} years từ model page")
        return sorted_years
    
    def extract_versions_from_year_page(self, year_url, year):
        """
        Trích xuất danh sách versions từ trang year
        
        Args:
            year_url (str): URL của trang year
            year (int): Năm
            
        Returns:
            list: Danh sách versions
        """
        logger.info(f"Scraping versions từ year {year}: {year_url}")
        
        # Delay để tránh bị block
        ScrapingUtils.delay_between_requests()
        
        soup = ScrapingUtils.get_page_content(year_url, self.session)
        if not soup:
            logger.error(f"Không thể lấy trang year: {year_url}")
            return []
        
        versions = []
        
        # Tìm các phiên bản trong trang
        # Thường các phiên bản sẽ xuất hiện trong tiêu đề của các tin đăng bán xe
        car_listings = soup.find_all(['h3', 'h4', 'h5'], class_=lambda x: x is None or 'title' in str(x).lower())
        
        for listing in car_listings:
            title_text = ScrapingUtils.clean_text(listing.get_text())
            
            if title_text:
                # Trích xuất tên phiên bản từ tiêu đề
                # Ví dụ: "Audi A4 2.0 TFSI - 2016" -> "2.0 TFSI"
                version_match = self.extract_version_from_title(title_text)
                
                if version_match:
                    version_name = version_match.strip()
                    
                    # Validate version name
                    if self.validate_version_name(version_name):
                        version_info = {
                            'name': version_name,
                            'year': year,
                            'url': year_url
                        }
                        
                        versions.append(version_info)
                        logger.info(f"Tìm thấy version: {version_name} ({year})")
        
        # Loại bỏ duplicate versions
        unique_versions = {}
        for version in versions:
            key = f"{version['name']}_{version['year']}"
            unique_versions[key] = version
        
        unique_list = list(unique_versions.values())
        logger.info(f"Đã scrape {len(unique_list)} unique versions cho year {year}")
        return unique_list
    
    def extract_version_from_title(self, title):
        """
        Trích xuất tên version từ tiêu đề tin đăng
        
        Args:
            title (str): Tiêu đề tin đăng
            
        Returns:
            str: Tên version hoặc None
        """
        # Patterns để trích xuất version
        patterns = [
            r'(\d+\.\d+\s*[A-Z]+(?:\s+[A-Z]+)*)',  # 2.0 TFSI, 1.8 TFSI
            r'(\d+\.\d+[A-Z]?)',  # 2.0T, 1.8L
            r'([A-Z]+\s*\d+)',  # TFSI 2.0
            r'(Quattro)',  # Quattro
            r'(Advanced\s+Plus)',  # Advanced Plus
            r'(Black\s+Edition)',  # Black Edition
            r'(Premium)',  # Premium
            r'(Prestige)',  # Prestige
        ]
        
        for pattern in patterns:
            match = re.search(pattern, title, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None
    
    def validate_version_name(self, version_name):
        """
        Validate tên version
        
        Args:
            version_name (str): Tên version
            
        Returns:
            bool: True nếu hợp lệ
        """
        if not version_name or len(version_name.strip()) == 0:
            return False
        
        # Kiểm tra độ dài
        if len(version_name) > 200:
            return False
        
        # Bỏ qua các version name quá chung chung
        invalid_names = ['xe', 'oto', 'car', 'auto', 'mới', 'cũ', 'đẹp', 'tốt']
        if version_name.lower() in invalid_names:
            return False
        
        return True
    
    def insert_versions(self, brand_id, model_id, versions):
        """
        Insert danh sách versions vào database
        
        Args:
            brand_id (int): ID của brand
            model_id (int): ID của model
            versions (list): Danh sách versions
        """
        if not versions:
            return
        
        try:
            with self.connection.cursor() as cursor:
                for version in versions:
                    # Kiểm tra version đã tồn tại chưa
                    cursor.execute(
                        "SELECT id FROM car_version_year WHERE model_id = %s AND version_name = %s AND year = %s",
                        (model_id, version['name'], version['year'])
                    )
                    existing = cursor.fetchone()
                    
                    if existing:
                        logger.info(f"Version {version['name']} ({version['year']}) đã tồn tại")
                        continue
                    
                    # Insert version mới
                    sql = """
                        INSERT INTO car_version_year (brand_id, model_id, version_name, year, url) 
                        VALUES (%s, %s, %s, %s, %s)
                    """
                    cursor.execute(sql, (
                        brand_id, 
                        model_id, 
                        version['name'], 
                        version['year'], 
                        version['url']
                    ))
                    self.versions_scraped += 1
                    
                    logger.info(f"Đã insert version: {version['name']} ({version['year']})")
                
                self.connection.commit()
                
        except Exception as e:
            logger.error(f"Lỗi insert versions cho model_id {model_id}: {e}")
    
    def process_model(self, model_info):
        """
        Xử lý một model để scrape versions
        
        Args:
            model_info (tuple): Thông tin model
        """
        model_id, model_name, model_url, brand_id, brand_name = model_info
        
        logger.info(f"Xử lý model: {brand_name} {model_name}")
        
        try:
            # 1. Scrape years từ trang model
            years = self.extract_years_from_model_page(model_url)
            
            if not years:
                logger.warning(f"Không tìm thấy years cho model: {brand_name} {model_name}")
                self.models_skipped += 1
                return
            
            # 2. Scrape versions từ từng year
            all_versions = []
            
            for year_info in years:
                versions = self.extract_versions_from_year_page(year_info['url'], year_info['year'])
                all_versions.extend(versions)
            
            # 3. Insert versions vào database
            if all_versions:
                self.insert_versions(brand_id, model_id, all_versions)
                logger.info(f"Đã xử lý {len(all_versions)} versions cho {brand_name} {model_name}")
            else:
                logger.warning(f"Không tìm thấy versions cho model: {brand_name} {model_name}")
                self.models_skipped += 1
            
            self.models_processed += 1
            
        except Exception as e:
            logger.error(f"Lỗi xử lý model {brand_name} {model_name}: {e}")
            self.models_skipped += 1
    
    def run_scraping(self, limit=None, offset=0):
        """
        Chạy toàn bộ quá trình scraping versions
        
        Args:
            limit (int): Số lượng models tối đa để xử lý
            offset (int): Vị trí bắt đầu
        """
        logger.info("=== BẮT ĐẦU SCRAPING BONBANH.COM VERSIONS ===")
        
        if not self.connect_db():
            return False
        
        try:
            # 1. Lấy danh sách models cần xử lý
            models = self.get_models_to_process(limit, offset)
            if not models:
                logger.info("Không có models nào cần xử lý versions")
                return True
            
            # 2. Xử lý từng model
            for i, model_info in enumerate(models, 1):
                logger.info(f"Xử lý model {i}/{len(models)}")
                self.process_model(model_info)
                
                # Progress report mỗi 10 models
                if i % 10 == 0:
                    logger.info(f"Tiến độ: {i}/{len(models)} models - {self.versions_scraped} versions")
            
            # 3. Thống kê kết quả
            logger.info("=== KẾT QUẢ SCRAPING VERSIONS ===")
            logger.info(f"Models đã xử lý: {self.models_processed}")
            logger.info(f"Models bị bỏ qua: {self.models_skipped}")
            logger.info(f"Versions đã scrape: {self.versions_scraped}")
            
            return True
            
        except Exception as e:
            logger.error(f"Lỗi trong quá trình scraping versions: {e}")
            return False
        finally:
            self.disconnect_db()

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Bonbanh Versions Scraper')
    parser.add_argument('--limit', type=int, help='Số lượng models tối đa để xử lý')
    parser.add_argument('--offset', type=int, default=0, help='Vị trí bắt đầu')
    
    args = parser.parse_args()
    
    scraper = BonbanhVersionsScraper()
    success = scraper.run_scraping(limit=args.limit, offset=args.offset)
    
    if success:
        print("✅ Scraping versions hoàn tất thành công!")
    else:
        print("❌ Scraping versions thất bại!")

if __name__ == "__main__":
    main()
