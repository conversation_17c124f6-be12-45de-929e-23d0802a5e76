"""
Script để kiểm tra dữ liệu versions đã scrape
"""

import pymysql
from config import Config

def check_versions_data():
    """Kiểm tra dữ liệu versions trong database"""
    try:
        connection = pymysql.connect(**Config.get_db_config())
        
        with connection.cursor() as cursor:
            # Đếm tổng số versions
            cursor.execute("SELECT COUNT(*) FROM car_version_year")
            total_versions = cursor.fetchone()[0]
            print(f"📊 Tổng số versions: {total_versions}")
            
            # Thống kê versions theo brand
            cursor.execute("""
                SELECT b.name as brand, COUNT(cv.id) as version_count
                FROM brands b
                LEFT JOIN car_version_year cv ON b.id = cv.brand_id
                GROUP BY b.id, b.name
                HAVING version_count > 0
                ORDER BY version_count DESC
                LIMIT 10
            """)
            
            brand_stats = cursor.fetchall()
            print("\n🏆 TOP BRANDS CÓ NHIỀU VERSIONS:")
            print("="*50)
            for brand, count in brand_stats:
                print(f"{brand:<20} - {count:3d} versions")
            
            # Thống kê versions theo năm
            cursor.execute("""
                SELECT year, COUNT(*) as count
                FROM car_version_year
                GROUP BY year
                ORDER BY year DESC
                LIMIT 10
            """)
            
            year_stats = cursor.fetchall()
            print("\n📅 VERSIONS THEO NĂM:")
            print("="*50)
            for year, count in year_stats:
                print(f"{year:<10} - {count:3d} versions")
            
            # Sample versions cho Audi A4
            cursor.execute("""
                SELECT cv.version_name, cv.year, cv.url
                FROM car_version_year cv
                JOIN models m ON cv.model_id = m.id
                JOIN brands b ON cv.brand_id = b.id
                WHERE b.name = 'Audi' AND m.name = 'A4'
                ORDER BY cv.year DESC, cv.version_name
                LIMIT 10
            """)
            
            audi_a4_versions = cursor.fetchall()
            print(f"\n🚗 SAMPLE VERSIONS CHO AUDI A4:")
            print("="*50)
            for version, year, url in audi_a4_versions:
                print(f"{year} - {version}")
            
            # Kiểm tra models chưa có versions
            cursor.execute("""
                SELECT b.name as brand, m.name as model
                FROM models m
                JOIN brands b ON m.brand_id = b.id
                LEFT JOIN car_version_year cv ON m.id = cv.model_id
                WHERE cv.id IS NULL
                ORDER BY b.name, m.name
                LIMIT 10
            """)
            
            models_without_versions = cursor.fetchall()
            print(f"\n❓ MODELS CHƯA CÓ VERSIONS (10 đầu tiên):")
            print("="*50)
            for brand, model in models_without_versions:
                print(f"{brand} {model}")
        
        connection.close()
        print(f"\n✅ Kiểm tra dữ liệu versions hoàn tất!")
        
    except Exception as e:
        print(f"❌ Lỗi kiểm tra dữ liệu: {e}")

if __name__ == "__main__":
    check_versions_data()
