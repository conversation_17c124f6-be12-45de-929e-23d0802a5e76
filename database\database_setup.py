"""
Database setup script for Bonbanh scraper
Tạo c<PERSON>c bảng brands, models và car_version_year với foreign key relationship
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pymysql
import logging
from config import Config

# Setup logging
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(Config.LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseSetup:
    """Class để thiết lập database và tạo bảng"""
    
    def __init__(self):
        self.config = Config.get_db_config()
        self.connection = None
    
    def connect(self):
        """Kết nối đến MySQL database"""
        try:
            self.connection = pymysql.connect(**self.config)
            logger.info("Kết nối database thành công")
            return True
        except Exception as e:
            logger.error(f"Lỗi kết nối database: {e}")
            return False
    
    def disconnect(self):
        """Đóng kết nối database"""
        if self.connection:
            self.connection.close()
            logger.info("Đã đóng kết nối database")
    
    def create_brands_table(self):
        """Tạo bảng brands"""
        sql = """
        CREATE TABLE IF NOT EXISTS brands (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            slug VARCHAR(100) NOT NULL UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_name (name),
            INDEX idx_slug (slug)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql)
                self.connection.commit()
                logger.info("Tạo bảng brands thành công")
                return True
        except Exception as e:
            logger.error(f"Lỗi tạo bảng brands: {e}")
            return False
    
    def create_models_table(self):
        """Tạo bảng models với foreign key đến brands"""
        sql = """
        CREATE TABLE IF NOT EXISTS models (
            id INT AUTO_INCREMENT PRIMARY KEY,
            brand_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            slug VARCHAR(100) NOT NULL,
            url VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE CASCADE,
            UNIQUE KEY unique_brand_model (brand_id, name),
            INDEX idx_brand_id (brand_id),
            INDEX idx_name (name),
            INDEX idx_slug (slug),
            INDEX idx_url (url)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql)
                self.connection.commit()
                logger.info("Tạo bảng models thành công")
                return True
        except Exception as e:
            logger.error(f"Lỗi tạo bảng models: {e}")
            return False
    
    def create_car_version_year_table(self):
        """Tạo bảng car_version_year"""
        sql = """
        CREATE TABLE IF NOT EXISTS car_version_year (
            id INT AUTO_INCREMENT PRIMARY KEY,
            brand_id INT NOT NULL,
            model_id INT NOT NULL,
            version_name VARCHAR(200) NOT NULL,
            year INT NOT NULL,
            url VARCHAR(500) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE CASCADE,
            FOREIGN KEY (model_id) REFERENCES models(id) ON DELETE CASCADE,
            UNIQUE KEY unique_version (model_id, version_name, year),
            INDEX idx_brand_model (brand_id, model_id),
            INDEX idx_year (year),
            INDEX idx_version_name (version_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql)
                self.connection.commit()
                logger.info("Tạo bảng car_version_year thành công")
                return True
        except Exception as e:
            logger.error(f"Lỗi tạo bảng car_version_year: {e}")
            return False
    
    def add_url_column_to_models(self):
        """Thêm cột url vào bảng models nếu chưa có"""
        try:
            with self.connection.cursor() as cursor:
                # Kiểm tra xem cột url đã tồn tại chưa
                cursor.execute("SHOW COLUMNS FROM models LIKE 'url'")
                result = cursor.fetchone()
                
                if not result:
                    # Thêm cột url
                    cursor.execute("ALTER TABLE models ADD COLUMN url VARCHAR(255)")
                    self.connection.commit()
                    logger.info("Đã thêm cột url vào bảng models")
                else:
                    logger.info("Cột url đã tồn tại trong bảng models")
                
                return True
        except Exception as e:
            logger.error(f"Lỗi thêm cột url: {e}")
            return False
    
    def drop_tables(self):
        """Xóa các bảng cũ (nếu tồn tại)"""
        try:
            with self.connection.cursor() as cursor:
                # Xóa bảng theo thứ tự (foreign key dependencies)
                cursor.execute("DROP TABLE IF EXISTS car_version_year")
                cursor.execute("DROP TABLE IF EXISTS models")
                cursor.execute("DROP TABLE IF EXISTS brands")
                self.connection.commit()
                logger.info("Đã xóa các bảng cũ")
                return True
        except Exception as e:
            logger.error(f"Lỗi xóa bảng: {e}")
            return False
    
    def setup_database(self, drop_existing=False):
        """Thiết lập toàn bộ database"""
        if not self.connect():
            return False
        
        try:
            if drop_existing:
                logger.info("Xóa các bảng cũ...")
                self.drop_tables()
            
            logger.info("Tạo bảng brands...")
            if not self.create_brands_table():
                return False
            
            logger.info("Tạo bảng models...")
            if not self.create_models_table():
                return False
            
            logger.info("Tạo bảng car_version_year...")
            if not self.create_car_version_year_table():
                return False
            
            # Thêm cột url nếu chưa có (cho trường hợp upgrade)
            if not drop_existing:
                logger.info("Kiểm tra và thêm cột url...")
                self.add_url_column_to_models()
            
            logger.info("Thiết lập database hoàn tất!")
            return True
            
        except Exception as e:
            logger.error(f"Lỗi thiết lập database: {e}")
            return False
        finally:
            self.disconnect()

def main():
    """Main function để chạy setup"""
    print("=== THIẾT LẬP DATABASE CHO BONBANH SCRAPER ===")
    print("1. Giữ nguyên dữ liệu cũ (nếu có)")
    print("2. Xóa và tạo lại toàn bộ")
    
    choice = input("Chọn tùy chọn (1/2): ").strip()
    
    setup = DatabaseSetup()
    
    if choice == "2":
        success = setup.setup_database(drop_existing=True)
    else:
        success = setup.setup_database(drop_existing=False)
    
    if success:
        print("✅ Thiết lập database thành công!")
    else:
        print("❌ Thiết lập database thất bại!")

if __name__ == "__main__":
    main()
