# C<PERSON><PERSON> lệnh quan trọng cho dự án

## <PERSON><PERSON><PERSON> Windows cơ bản
- `dir` - li<PERSON><PERSON> kê files và folders
- `cd` - thay đ<PERSON>i directory
- `type` - xem nội dung file
- `findstr` - tìm kiếm text trong file
- `mkdir` - tạo directory
- `del` - xóa file
- `rmdir` - xóa directory

## Python và Package Management
- `python -m pip install -r requirements.txt` - cài đặt dependencies
- `python -m pip freeze > requirements.txt` - export dependencies
- `python scraper.py` - chạy script scraping
- `python setup.py` - chạy setup database

## Database Commands
- `mysql -u root -p` - kết nối MySQL
- `mysql -u root -p xehoi_pro < setup.sql` - import SQL file

## Git Commands
- `git init` - khởi tạo git repository
- `git add .` - add tất cả files
- `git commit -m "message"` - commit changes
- `git status` - kiểm tra status

## Testing và Development
- `python -m pytest` - chạy tests (nếu có)
- `python -c "import requests; print('OK')"` - test import modules