"""
Database models definition for Bonbanh scraper
<PERSON><PERSON><PERSON> nghĩa schema cho các bảng database
"""

# Database Schema Definitions

BRANDS_TABLE_SCHEMA = """
CREATE TABLE IF NOT EXISTS brands (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_slug (slug)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
"""

MODELS_TABLE_SCHEMA = """
CREATE TABLE IF NOT EXISTS models (
    id INT AUTO_INCREMENT PRIMARY KEY,
    brand_id INT NOT NULL,
    name <PERSON><PERSON>HA<PERSON>(100) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE CASCADE,
    UNIQUE KEY unique_brand_model (brand_id, name),
    INDEX idx_brand_id (brand_id),
    INDEX idx_name (name),
    INDEX idx_slug (slug),
    INDEX idx_url (url)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
"""

CAR_VERSION_YEAR_TABLE_SCHEMA = """
CREATE TABLE IF NOT EXISTS car_version_year (
    id INT AUTO_INCREMENT PRIMARY KEY,
    brand_id INT NOT NULL,
    model_id INT NOT NULL,
    version_name VARCHAR(200) NOT NULL,
    year INT NOT NULL,
    url VARCHAR(500) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE CASCADE,
    FOREIGN KEY (model_id) REFERENCES models(id) ON DELETE CASCADE,
    UNIQUE KEY unique_version (model_id, version_name, year),
    INDEX idx_brand_model (brand_id, model_id),
    INDEX idx_year (year),
    INDEX idx_version_name (version_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
"""

# Table relationships documentation
"""
Database Relationships:

1. brands (1) -> models (N)
   - brands.id -> models.brand_id

2. brands (1) -> car_version_year (N)
   - brands.id -> car_version_year.brand_id

3. models (1) -> car_version_year (N)
   - models.id -> car_version_year.model_id

Data Flow:
1. Scrape brands from homepage -> brands table
2. Scrape models for each brand -> models table
3. Scrape versions/years for each model -> car_version_year table

Example Data:
- Brand: Toyota (id=1)
- Model: Vios (id=1, brand_id=1)
- Version: Vios 1.5E 2023 (model_id=1, brand_id=1, year=2023)
"""

# Utility queries
COMMON_QUERIES = {
    'get_all_brands': "SELECT * FROM brands ORDER BY name",
    
    'get_models_by_brand': """
        SELECT m.* FROM models m 
        JOIN brands b ON m.brand_id = b.id 
        WHERE b.name = %s 
        ORDER BY m.name
    """,
    
    'get_versions_by_model': """
        SELECT cv.* FROM car_version_year cv
        JOIN models m ON cv.model_id = m.id
        JOIN brands b ON cv.brand_id = b.id
        WHERE b.name = %s AND m.name = %s
        ORDER BY cv.year DESC, cv.version_name
    """,
    
    'get_models_without_versions': """
        SELECT b.name as brand, m.name as model, m.url
        FROM models m
        JOIN brands b ON m.brand_id = b.id
        LEFT JOIN car_version_year cv ON m.id = cv.model_id
        WHERE cv.id IS NULL
        ORDER BY b.name, m.name
    """,
    
    'count_versions_by_year': """
        SELECT year, COUNT(*) as count
        FROM car_version_year
        GROUP BY year
        ORDER BY year DESC
    """,
    
    'top_brands_by_versions': """
        SELECT b.name, COUNT(cv.id) as version_count
        FROM brands b
        LEFT JOIN car_version_year cv ON b.id = cv.brand_id
        GROUP BY b.id, b.name
        ORDER BY version_count DESC
        LIMIT 10
    """
}
