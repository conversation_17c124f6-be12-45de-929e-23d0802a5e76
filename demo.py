"""
Demo script để showcase dữ liệu đã scrape từ bonbanh.com
"""

import pymysql
from config import Config


def demo_showcase():
    """Demo showcase dữ liệu đã scrape"""
    try:
        connection = pymysql.connect(**Config.get_db_config())

        with connection.cursor() as cursor:
            print("🚗 BONBANH.COM SCRAPING DEMO")
            print("="*60)

            # 1. Thống kê tổng quan
            cursor.execute("SELECT COUNT(*) FROM brands")
            total_brands = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM models")
            total_models = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM car_version_year")
            total_versions = cursor.fetchone()[0]

            print(f"📊 TỔNG QUAN:")
            print(f"   - Brands: {total_brands}")
            print(f"   - Models: {total_models}")
            print(f"   - Versions: {total_versions}")

            # Random brand showcase
            cursor.execute("SELECT id, name FROM brands ORDER BY RAND() LIMIT 3")
            random_brands = cursor.fetchall()

            print(f"\n🎲 SHOWCASE 3 BRANDS NGẪU NHIÊN:")

            for brand_id, brand_name in random_brands:
                cursor.execute("""
                    SELECT name FROM models 
                    WHERE brand_id = %s 
                    ORDER BY name 
                    LIMIT 6
                """, (brand_id,))

                models = cursor.fetchall()
                models_text = ", ".join([model[0] for model in models])
                if len(models) == 6:
                    cursor.execute("SELECT COUNT(*) FROM models WHERE brand_id = %s", (brand_id,))
                    total = cursor.fetchone()[0]
                    if total > 6:
                        models_text += f" (và {total-6} models khác)"

                print(f"\n🏷️  {brand_name}:")
                print(f"   {models_text}")

            # Brands phổ biến
            print(f"\n🔥 TOP 5 BRANDS PHỔ BIẾN NHẤT:")
            cursor.execute("""
                SELECT b.name, COUNT(m.id) as model_count 
                FROM brands b 
                LEFT JOIN models m ON b.id = m.brand_id 
                GROUP BY b.id, b.name 
                ORDER BY model_count DESC 
                LIMIT 5
            """)

            top_brands = cursor.fetchall()
            for i, (brand, count) in enumerate(top_brands, 1):
                print(f"   {i}. {brand} - {count} models")

            # Brands ít models
            print(f"\n🔍 BRANDS ÍT MODELS NHẤT:")
            cursor.execute("""
                SELECT b.name, COUNT(m.id) as model_count 
                FROM brands b 
                LEFT JOIN models m ON b.id = m.brand_id 
                GROUP BY b.id, b.name 
                ORDER BY model_count ASC 
                LIMIT 5
            """)

            least_brands = cursor.fetchall()
            for i, (brand, count) in enumerate(least_brands, 1):
                print(f"   {i}. {brand} - {count} models")

            # Sample queries
            print(f"\n💡 MỘT SỐ QUERY MẪU:")

            # Tìm tất cả Toyota models
            cursor.execute("""
                SELECT m.name 
                FROM models m 
                JOIN brands b ON m.brand_id = b.id 
                WHERE b.name = 'Toyota' 
                ORDER BY m.name
            """)
            toyota_models = cursor.fetchall()
            print(f"\n   🔍 Tất cả models của Toyota ({len(toyota_models)} models):")
            toyota_text = ", ".join([model[0] for model in toyota_models[:10]])
            if len(toyota_models) > 10:
                toyota_text += "..."
            print(f"      {toyota_text}")

            # Tìm models có chứa "Cross"
            cursor.execute("""
                SELECT b.name as brand, m.name as model
                FROM models m 
                JOIN brands b ON m.brand_id = b.id 
                WHERE m.name LIKE '%Cross%'
                ORDER BY b.name, m.name
            """)
            cross_models = cursor.fetchall()
            print(f"\n   🔍 Models có chứa 'Cross' ({len(cross_models)} models):")
            for brand, model in cross_models:
                print(f"      • {brand} {model}")

            # Brands có models nhiều nhất và ít nhất
            cursor.execute("""
                SELECT 
                    MAX(model_count) as max_models,
                    MIN(model_count) as min_models
                FROM (
                    SELECT COUNT(m.id) as model_count 
                    FROM brands b 
                    LEFT JOIN models m ON b.id = m.brand_id 
                    GROUP BY b.id
                ) as counts
            """)
            max_models, min_models = cursor.fetchone()
            print(f"\n📈 THỐNG KÊ PHÂN BỐ:")
            print(f"   • Brand có nhiều models nhất: {max_models} models")
            print(f"   • Brand có ít models nhất: {min_models} models")
            print(f"   • Chênh lệch: {max_models - min_models} models")

        connection.close()

        print(f"\n✨ Demo hoàn tất! Dữ liệu đã sẵn sàng để sử dụng.")
        print(f"💡 Bạn có thể sử dụng dữ liệu này để:")
        print(f"   • Tạo dropdown chọn hãng xe và dòng xe")
        print(f"   • Phân tích thị trường ô tô")
        print(f"   • Tạo content về xe hơi")
        print(f"   • Xây dựng hệ thống so sánh xe")

    except Exception as e:
        print(f"❌ Lỗi demo: {e}")


if __name__ == "__main__":
    demo_data()
