# Coding Style và Conventions

## Python Style Guidelines
- Sử dụng PEP 8 standard
- Snake_case cho variables và functions
- PascalCase cho classes
- UPPER_CASE cho constants
- Indentation: 4 spaces

## Naming Conventions
- Files: snake_case.py
- Classes: PascalCase (VD: <PERSON><PERSON><PERSON><PERSON><PERSON>, DatabaseManager)
- Functions: snake_case (VD: scrape_brands, insert_data)
- Variables: snake_case (VD: brand_name, model_list)
- Constants: UPPER_CASE (VD: MAX_RETRIES, TIMEOUT)

## Code Organization
- Separate concerns: scraping, database, utilities
- Use classes for complex functionality
- Keep functions small and focused
- Add docstrings for classes and functions

## Error Handling
- Use try-except blocks for database operations
- Log errors appropriately
- Implement retry logic for network requests
- Validate data before insertion

## Comments và Documentation
- Vietnamese comments cho business logic
- English comments cho technical details
- Docstrings in English
- README in Vietnamese