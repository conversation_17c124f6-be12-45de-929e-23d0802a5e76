"""
Bonbanh.com scraper for car brands and models
Scraping dữ liệu hãng xe và dòng xe từ bonbanh.com
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pymysql
import logging
from bs4 import BeautifulSoup
from config import Config
from utils import ScrapingUtils
import time

# Setup logging
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(Config.LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class BonbanhBrandsScraper:
    """Main scraper class for bonbanh.com brands and models"""

    def __init__(self):
        self.config = Config.get_db_config()
        self.connection = None
        self.session = ScrapingUtils.create_session()
        self.brands_scraped = 0
        self.models_scraped = 0

    def connect_db(self):
        """Kết nối đến database"""
        try:
            self.connection = pymysql.connect(**self.config)
            logger.info("Kết nối database thành công")
            return True
        except Exception as e:
            logger.error(f"Lỗi kết nối database: {e}")
            return False

    def disconnect_db(self):
        """Đóng kết nối database"""
        if self.connection:
            self.connection.close()
            logger.info("Đã đóng kết nối database")

    def scrape_brands_from_homepage(self):
        """
        Scrape danh sách brands từ trang chủ bonbanh.com

        Returns:
            list: Danh sách dict chứa thông tin brands
        """
        logger.info("Bắt đầu scraping brands từ trang chủ...")

        soup = ScrapingUtils.get_page_content(Config.BRANDS_URL, self.session)
        if not soup:
            logger.error("Không thể lấy nội dung trang chủ")
            return []

        brands = []

        # Tìm phần "Tìm theo hãng xe" - dựa trên HTML thực tế
        # Tìm tất cả links có href chứa "oto/" và không chứa javascript
        all_links = soup.find_all('a', href=True)

        for link in all_links:
            href = link.get('href', '')
            brand_name = ScrapingUtils.clean_text(link.get_text())

            # Chỉ lấy links dạng oto/brand-name (không có thêm gì phía sau)
            if not href.startswith('oto/') or '/' in href[4:] or not brand_name:
                continue

            # Bỏ qua link "Tất cả hãng" và các link javascript
            if 'javascript:void' in href or brand_name in ['Tất cả hãng', 'Tất cả']:
                continue

            # Trích xuất brand slug từ URL
            brand_slug = ScrapingUtils.extract_brand_slug_from_url(href)
            if not brand_slug:
                logger.warning(f"Không thể trích xuất slug từ URL: {href}")
                continue

            # Validate brand name
            if not ScrapingUtils.validate_brand_name(brand_name):
                logger.warning(f"Tên brand không hợp lệ: {brand_name}")
                continue

            brand_info = {
                'name': brand_name,
                'slug': brand_slug,
                'url': f"{Config.BASE_URL}/{href}"
            }

            brands.append(brand_info)
            logger.info(f"Tìm thấy brand: {brand_name} ({brand_slug})")

        logger.info(f"Đã scrape {len(brands)} brands từ trang chủ")
        return brands

    def scrape_models_for_brand(self, brand_info):
        """
        Scrape danh sách models cho một brand cụ thể

        Args:
            brand_info (dict): Thông tin brand

        Returns:
            list: Danh sách models của brand
        """
        brand_name = brand_info['name']
        brand_url = brand_info['url']

        logger.info(f"Scraping models cho brand: {brand_name}")

        # Delay để tránh bị block
        ScrapingUtils.delay_between_requests()

        soup = ScrapingUtils.get_page_content(brand_url, self.session)
        if not soup:
            logger.error(f"Không thể lấy trang brand: {brand_name}")
            return []

        models = []

        # Dựa trên HTML thực tế, tìm phần sidebar chứa danh sách hãng xe
        # Tìm phần có chứa brand hiện tại và lấy models từ đó
        brand_sections = soup.find_all('li')

        for section in brand_sections:
            # Tìm link có text trùng với brand name
            brand_link = section.find('a', string=lambda text: text and text.strip().lower() == brand_name.lower())

            if brand_link:
                # Tìm danh sách models trong cùng section
                model_list = section.find('ul') or section.find_next_sibling('ul')

                if model_list:
                    model_links = model_list.find_all('a', href=True)

                    for link in model_links:
                        href = link.get('href', '')
                        model_name = ScrapingUtils.clean_text(link.get_text())

                        # Bỏ qua các link không phải model
                        if not model_name or model_name in ['Tất cả', 'Khác']:
                            continue

                        # Kiểm tra URL có chứa brand slug không (dạng oto/brand-model)
                        if not href.startswith(f'oto/{brand_info["slug"]}-'):
                            continue

                        # Validate model name
                        if not ScrapingUtils.validate_model_name(model_name):
                            logger.warning(f"Tên model không hợp lệ: {model_name}")
                            continue

                        model_slug = ScrapingUtils.create_slug(model_name)
                        model_url = f"{Config.BASE_URL}/{href}"

                        model_info = {
                            'name': model_name,
                            'slug': model_slug,
                            'url': model_url
                        }

                        models.append(model_info)
                        logger.info(f"Tìm thấy model: {model_name} cho brand {brand_name}")

                break  # Đã tìm thấy brand, không cần tìm tiếp

        # Nếu không tìm thấy models theo cách trên, thử cách khác
        if not models:
            # Tìm tất cả links có href chứa brand slug
            all_links = soup.find_all('a', href=True)

            for link in all_links:
                href = link.get('href', '')
                model_name = ScrapingUtils.clean_text(link.get_text())

                # Kiểm tra URL có dạng oto/brand-model không
                if href.startswith(f'oto/{brand_info["slug"]}-') and model_name:
                    # Validate model name
                    if not ScrapingUtils.validate_model_name(model_name):
                        continue

                    model_slug = ScrapingUtils.create_slug(model_name)
                    model_url = f"{Config.BASE_URL}/{href}"

                    model_info = {
                        'name': model_name,
                        'slug': model_slug,
                        'url': model_url
                    }

                    models.append(model_info)
                    logger.info(f"Tìm thấy model: {model_name} cho brand {brand_name}")

        logger.info(f"Đã scrape {len(models)} models cho brand {brand_name}")
        return models

    def insert_brand(self, brand_info):
        """
        Insert brand vào database

        Args:
            brand_info (dict): Thông tin brand

        Returns:
            int: ID của brand đã insert, hoặc None nếu thất bại
        """
        try:
            with self.connection.cursor() as cursor:
                # Kiểm tra brand đã tồn tại chưa
                cursor.execute("SELECT id FROM brands WHERE name = %s", (brand_info['name'],))
                existing = cursor.fetchone()

                if existing:
                    logger.info(f"Brand {brand_info['name']} đã tồn tại với ID: {existing[0]}")
                    return existing[0]

                # Insert brand mới
                sql = "INSERT INTO brands (name, slug) VALUES (%s, %s)"
                cursor.execute(sql, (brand_info['name'], brand_info['slug']))
                self.connection.commit()

                brand_id = cursor.lastrowid
                logger.info(f"Đã insert brand: {brand_info['name']} với ID: {brand_id}")
                self.brands_scraped += 1

                return brand_id

        except Exception as e:
            logger.error(f"Lỗi insert brand {brand_info['name']}: {e}")
            return None

    def insert_models(self, brand_id, models):
        """
        Insert danh sách models vào database

        Args:
            brand_id (int): ID của brand
            models (list): Danh sách models
        """
        if not models:
            return

        try:
            with self.connection.cursor() as cursor:
                for model in models:
                    # Kiểm tra model đã tồn tại chưa
                    cursor.execute(
                        "SELECT id FROM models WHERE brand_id = %s AND name = %s",
                        (brand_id, model['name'])
                    )
                    existing = cursor.fetchone()

                    if existing:
                        logger.info(f"Model {model['name']} đã tồn tại")
                        continue

                    # Insert model mới với URL
                    sql = "INSERT INTO models (brand_id, name, slug, url) VALUES (%s, %s, %s, %s)"
                    cursor.execute(sql, (brand_id, model['name'], model['slug'], model['url']))
                    self.models_scraped += 1

                    logger.info(f"Đã insert model: {model['name']}")

                self.connection.commit()

        except Exception as e:
            logger.error(f"Lỗi insert models cho brand_id {brand_id}: {e}")

    def run_scraping(self):
        """Chạy toàn bộ quá trình scraping"""
        logger.info("=== BẮT ĐẦU SCRAPING BONBANH.COM BRANDS ===")

        if not self.connect_db():
            return False

        try:
            # 1. Scrape brands từ trang chủ
            brands = self.scrape_brands_from_homepage()
            if not brands:
                logger.error("Không scrape được brands nào")
                return False

            # 2. Xử lý từng brand
            for i, brand_info in enumerate(brands, 1):
                logger.info(f"Xử lý brand {i}/{len(brands)}: {brand_info['name']}")

                # Insert brand vào database
                brand_id = self.insert_brand(brand_info)
                if not brand_id:
                    logger.error(f"Không thể insert brand: {brand_info['name']}")
                    continue

                # Scrape models cho brand này
                models = self.scrape_models_for_brand(brand_info)

                # Insert models vào database
                if models:
                    self.insert_models(brand_id, models)
                else:
                    logger.warning(f"Không tìm thấy models cho brand: {brand_info['name']}")

            # 3. Thống kê kết quả
            logger.info("=== KẾT QUẢ SCRAPING ===")
            logger.info(f"Brands đã scrape: {self.brands_scraped}")
            logger.info(f"Models đã scrape: {self.models_scraped}")

            return True

        except Exception as e:
            logger.error(f"Lỗi trong quá trình scraping: {e}")
            return False
        finally:
            self.disconnect_db()


def main():
    """Main function"""
    scraper = BonbanhBrandsScraper()
    success = scraper.run_scraping()

    if success:
        print("✅ Scraping brands hoàn tất thành công!")
    else:
        print("❌ Scraping brands thất bại!")


if __name__ == "__main__":
    main()
