"""
Configuration module for Bonbanh scraper
<PERSON><PERSON><PERSON> c<PERSON>u hình từ file .env
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for database and scraping settings"""
    
    # Database Configuration
    DB_HOST = os.getenv('DB_HOST', '127.0.0.1')
    DB_PORT = int(os.getenv('DB_PORT', 3306))
    DB_DATABASE = os.getenv('DB_DATABASE', 'xehoi_pro')
    DB_USERNAME = os.getenv('DB_USERNAME', 'root')
    DB_PASSWORD = os.getenv('DB_PASSWORD', 'pwdpwd')
    
    # Scraping Configuration
    SCRAPING_DELAY = int(os.getenv('SCRAPING_DELAY', 2))
    MAX_RETRIES = int(os.getenv('MAX_RETRIES', 3))
    TIMEOUT = int(os.getenv('TIMEOUT', 30))
    USER_AGENT = os.getenv('USER_AGENT', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
    
    # Logging Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'logs/scraper.log')
    
    # Website URLs
    BASE_URL = 'https://bonbanh.com'
    BRANDS_URL = 'https://bonbanh.com/'
    
    @classmethod
    def get_db_config(cls):
        """Return database configuration as dictionary"""
        return {
            'host': cls.DB_HOST,
            'port': cls.DB_PORT,
            'database': cls.DB_DATABASE,
            'user': cls.DB_USERNAME,
            'password': cls.DB_PASSWORD,
            'charset': 'utf8mb4'
        }
