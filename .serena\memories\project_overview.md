# Tổng quan dự án Xe Hơi Pro - Create Post 4

## <PERSON><PERSON><PERSON> đích dự án
- Scraping dữ liệu hãng xe (brands) và dòng xe (models) từ website bonbanh.com
- Lưu trữ dữ liệu vào MySQL database với 2 bảng riêng biệt
- <PERSON><PERSON><PERSON> vụ cho việc tạo post về xe hơi

## Tech Stack
- **Language**: Python
- **Database**: MySQL
- **Scraping**: BeautifulSoup/Selenium (sẽ được implement)
- **Database Connection**: pymysql hoặc mysql-connector-python
- **Environment**: Windows

## Cấu trúc Database
- Bảng `brands`: id, name, created_at, updated_at
- Bảng `models`: id, brand_id, name, created_at, updated_at
- Foreign key relationship: models.brand_id -> brands.id

## Cấu hình môi trường
- Database: MySQL local (127.0.0.1:3306)
- Database name: xehoi_pro
- Credentials: root/pwdpwd
- Scraping delay: 2 seconds
- Max retries: 3
- Timeout: 30 seconds