"""
Utility functions for Bonbanh scraper
<PERSON><PERSON><PERSON> hàm tiện ích cho việc scraping và xử lý dữ liệu
"""

import re
import time
import requests
from bs4 import BeautifulSoup
import logging
from config import Config

logger = logging.getLogger(__name__)

class ScrapingUtils:
    """Class chứa các hàm tiện ích cho scraping"""
    
    @staticmethod
    def create_session():
        """Tạo session với headers phù hợp"""
        session = requests.Session()
        session.headers.update({
            'User-Agent': Config.USER_AGENT,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        return session
    
    @staticmethod
    def get_page_content(url, session=None, retries=None):
        """
        Lấy nội dung trang web với retry logic
        
        Args:
            url (str): URL cần lấy
            session (requests.Session): Session để sử dụng
            retries (int): Số lần retry
            
        Returns:
            BeautifulSoup object hoặc None nếu thất bại
        """
        if session is None:
            session = ScrapingUtils.create_session()
        
        if retries is None:
            retries = Config.MAX_RETRIES
        
        for attempt in range(retries + 1):
            try:
                logger.info(f"Đang lấy trang: {url} (lần thử {attempt + 1})")
                
                response = session.get(url, timeout=Config.TIMEOUT)
                response.raise_for_status()
                
                # Parse HTML
                soup = BeautifulSoup(response.content, 'html.parser')
                
                logger.info(f"Lấy trang thành công: {url}")
                return soup
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"Lỗi lấy trang {url} (lần {attempt + 1}): {e}")
                
                if attempt < retries:
                    wait_time = (attempt + 1) * Config.SCRAPING_DELAY
                    logger.info(f"Chờ {wait_time} giây trước khi thử lại...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"Không thể lấy trang {url} sau {retries + 1} lần thử")
                    return None
            
            except Exception as e:
                logger.error(f"Lỗi không xác định khi lấy trang {url}: {e}")
                return None
        
        return None
    
    @staticmethod
    def clean_text(text):
        """
        Làm sạch text: loại bỏ khoảng trắng thừa, ký tự đặc biệt
        
        Args:
            text (str): Text cần làm sạch
            
        Returns:
            str: Text đã được làm sạch
        """
        if not text:
            return ""
        
        # Loại bỏ khoảng trắng thừa
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Loại bỏ các ký tự đặc biệt không mong muốn
        text = re.sub(r'[^\w\s\-\.]', '', text)
        
        return text
    
    @staticmethod
    def create_slug(text):
        """
        Tạo slug từ text (để làm URL-friendly)
        
        Args:
            text (str): Text cần tạo slug
            
        Returns:
            str: Slug đã được tạo
        """
        if not text:
            return ""
        
        # Chuyển về lowercase
        slug = text.lower()
        
        # Thay thế khoảng trắng bằng dấu gạch ngang
        slug = re.sub(r'\s+', '-', slug)
        
        # Loại bỏ các ký tự không phải chữ cái, số, hoặc dấu gạch ngang
        slug = re.sub(r'[^a-z0-9\-]', '', slug)
        
        # Loại bỏ dấu gạch ngang thừa
        slug = re.sub(r'-+', '-', slug)
        
        # Loại bỏ dấu gạch ngang ở đầu và cuối
        slug = slug.strip('-')
        
        return slug
    
    @staticmethod
    def extract_brand_slug_from_url(url):
        """
        Trích xuất brand slug từ URL
        
        Args:
            url (str): URL chứa brand slug
            
        Returns:
            str: Brand slug hoặc None nếu không tìm thấy
        """
        # Pattern để match URL dạng: oto/brand-name
        pattern = r'oto/([^/\s"]+)'
        match = re.search(pattern, url)
        
        if match:
            return match.group(1)
        
        return None
    
    @staticmethod
    def delay_between_requests():
        """Tạo delay giữa các request để tránh bị block"""
        time.sleep(Config.SCRAPING_DELAY)
    
    @staticmethod
    def validate_brand_name(name):
        """
        Validate tên brand
        
        Args:
            name (str): Tên brand cần validate
            
        Returns:
            bool: True nếu hợp lệ, False nếu không
        """
        if not name or len(name.strip()) == 0:
            return False
        
        # Kiểm tra độ dài
        if len(name) > 100:
            return False
        
        # Kiểm tra có chứa ký tự hợp lệ
        if not re.match(r'^[a-zA-Z0-9\s\-\.]+$', name):
            return False
        
        return True
    
    @staticmethod
    def validate_model_name(name):
        """
        Validate tên model
        
        Args:
            name (str): Tên model cần validate
            
        Returns:
            bool: True nếu hợp lệ, False nếu không
        """
        if not name or len(name.strip()) == 0:
            return False
        
        # Kiểm tra độ dài
        if len(name) > 100:
            return False
        
        # Kiểm tra có chứa ký tự hợp lệ
        if not re.match(r'^[a-zA-Z0-9\s\-\.\:]+$', name):
            return False
        
        return True
