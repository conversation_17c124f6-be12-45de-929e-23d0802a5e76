"""
Script để kiểm tra dữ liệu đã scrape trong database
"""

import pymysql
from config import Config

def check_database_data():
    """Kiểm tra dữ liệu trong database"""
    try:
        # Kết nối database
        connection = pymysql.connect(**Config.get_db_config())
        
        with connection.cursor() as cursor:
            # Đếm tổng số brands
            cursor.execute("SELECT COUNT(*) FROM brands")
            total_brands = cursor.fetchone()[0]
            print(f"📊 Tổng số brands: {total_brands}")
            
            # Đếm tổng số models
            cursor.execute("SELECT COUNT(*) FROM models")
            total_models = cursor.fetchone()[0]
            print(f"📊 Tổng số models: {total_models}")
            
            print("\n" + "="*50)
            print("🏆 TOP 10 BRANDS CÓ NHIỀU MODELS NHẤT:")
            print("="*50)
            
            # Lấy top 10 brands có nhiều models nhất
            cursor.execute("""
                SELECT b.name as brand, COUNT(m.id) as model_count 
                FROM brands b 
                LEFT JOIN models m ON b.id = m.brand_id 
                GROUP BY b.id, b.name 
                ORDER BY model_count DESC 
                LIMIT 10
            """)
            
            top_brands = cursor.fetchall()
            for i, (brand, count) in enumerate(top_brands, 1):
                print(f"{i:2d}. {brand:<15} - {count:3d} models")
            
            print("\n" + "="*50)
            print("📋 DANH SÁCH TẤT CẢ BRANDS:")
            print("="*50)
            
            # Lấy tất cả brands
            cursor.execute("SELECT name FROM brands ORDER BY name")
            all_brands = cursor.fetchall()
            
            brands_per_row = 3
            for i in range(0, len(all_brands), brands_per_row):
                row_brands = all_brands[i:i+brands_per_row]
                row_text = " | ".join([f"{brand[0]:<15}" for brand in row_brands])
                print(row_text)
            
            print("\n" + "="*50)
            print("🔍 SAMPLE MODELS CHO MỘT SỐ BRANDS:")
            print("="*50)
            
            # Lấy sample models cho một số brands phổ biến
            sample_brands = ['Toyota', 'Honda', 'BMW', 'Mercedes Benz', 'VinFast']
            
            for brand in sample_brands:
                cursor.execute("""
                    SELECT m.name 
                    FROM models m 
                    JOIN brands b ON m.brand_id = b.id 
                    WHERE b.name = %s 
                    ORDER BY m.name 
                    LIMIT 8
                """, (brand,))
                
                models = cursor.fetchall()
                if models:
                    print(f"\n🚗 {brand}:")
                    models_text = ", ".join([model[0] for model in models])
                    if len(models) == 8:
                        models_text += "..."
                    print(f"   {models_text}")
        
        connection.close()
        print(f"\n✅ Kiểm tra dữ liệu hoàn tất!")
        
    except Exception as e:
        print(f"❌ Lỗi kiểm tra dữ liệu: {e}")

if __name__ == "__main__":
    check_database_data()
